# Vue框架相关面试题

## Vue基础概念

1. Vue的双向绑定原理

**核心概念：**
Vue的双向绑定本质上是数据劫持 + 发布订阅模式的结合，通过响应式系统实现数据到视图的单向绑定，通过事件监听实现视图到数据的单向绑定。

**Vue2实现原理（Object.defineProperty）：**

1. **数据劫持阶段**
   - 通过Object.defineProperty劫持对象属性的getter/setter
   - 在getter中进行依赖收集（Dep.target指向当前Watcher）
   - 在setter中触发依赖更新（dep.notify()）

2. **依赖收集机制**
   - 每个响应式属性都有一个Dep实例，用于收集依赖
   - 每个组件都有一个Watcher实例，负责重新渲染
   - 建立Dep和Watcher的多对多关系

3. **更新派发流程**
   ```
   数据变更 → setter → dep.notify() → watcher.update() → 异步队列 → DOM更新
   ```

**Vue3实现原理（Proxy）：**

1. **Proxy代理**
   - 使用Proxy代理整个对象，而非单个属性
   - 可以监听属性的添加、删除、枚举等13种操作
   - 支持数组索引和length属性的监听

2. **响应式系统重构**
   - 使用WeakMap存储响应式对象的依赖关系
   - 引入effect函数替代Watcher
   - 使用track()收集依赖，trigger()触发更新

**关键区别：**
- **性能**：Proxy性能更好，Object.defineProperty需要递归遍历所有属性
- **功能**：Proxy支持数组变更检测，Object.defineProperty需要特殊处理
- **兼容性**：Object.defineProperty支持IE9+，Proxy不支持IE

**v-model语法糖实现：**
```javascript
// v-model="value" 编译后等价于：
h('input', {
  value: value,
  onInput: (e) => { value = e.target.value }
})
```
2. Vue的生命周期

**生命周期本质：**
Vue生命周期是组件从创建到销毁过程中的关键节点，每个节点都会调用对应的钩子函数，让开发者能在特定时机执行自定义逻辑。

**Vue2生命周期详解：**

1. **创建阶段**
   - **beforeCreate**：实例初始化后，数据观测和事件配置之前
     - 此时data、methods、computed都不可访问
     - 常用于插件开发中的一些初始化任务

   - **created**：实例创建完成，数据观测、属性和方法的运算已完成
     - 可以访问data、methods、computed
     - DOM还未生成，$el属性不存在
     - 适合进行数据初始化、API调用

2. **挂载阶段**
   - **beforeMount**：挂载开始之前，render函数首次被调用
     - 模板编译完成，虚拟DOM已生成
     - 真实DOM还未创建和挂载

   - **mounted**：实例挂载完成，真实DOM已创建
     - 可以访问$el和DOM元素
     - 适合DOM操作、第三方库初始化、启动定时器

3. **更新阶段**
   - **beforeUpdate**：数据更新时调用，虚拟DOM重新渲染之前
     - 可以在此阶段进一步修改状态，不会触发重渲染

   - **updated**：虚拟DOM重新渲染和打补丁完成后调用
     - DOM已更新完成，可以进行DOM相关操作
     - 避免在此阶段修改数据，可能导致无限循环

4. **销毁阶段**
   - **beforeDestroy**：实例销毁之前调用
     - 实例仍然完全可用
     - 适合清理定时器、解绑事件监听器、取消网络请求

   - **destroyed**：实例销毁后调用
     - 所有指令解绑，事件监听器移除，子实例销毁

**Vue3生命周期变化：**

1. **Composition API对应关系**
   ```javascript
   // Options API → Composition API
   beforeCreate  → setup()
   created       → setup()
   beforeMount   → onBeforeMount
   mounted       → onMounted
   beforeUpdate  → onBeforeUpdate
   updated       → onUpdated
   beforeDestroy → onBeforeUnmount
   destroyed     → onUnmounted
   ```

2. **新增钩子**
   - **onRenderTracked**：跟踪虚拟DOM重新渲染时调用
   - **onRenderTriggered**：虚拟DOM重新渲染被触发时调用
   - **onActivated/onDeactivated**：keep-alive组件激活/停用时调用

**执行顺序（父子组件）：**
```
父beforeCreate → 父created → 父beforeMount →
子beforeCreate → 子created → 子beforeMount → 子mounted →
父mounted
```

**关键应用场景：**
- **created**：数据初始化、API调用
- **mounted**：DOM操作、第三方库初始化
- **beforeDestroy/onBeforeUnmount**：资源清理、内存泄漏防护
3. Vue中key的作用

**核心作用：**
key是Vue虚拟DOM算法中用于识别VNode的唯一标识，主要用于优化列表渲染的性能和确保组件状态的正确性。

**底层原理：**

1. **虚拟DOM diff算法中的作用**
   - Vue使用key来判断两个节点是否为同一个节点
   - 在updateChildren函数中，通过key进行节点匹配
   - 相同key的节点会被认为是同一个节点，进行patch更新而非重新创建

2. **节点复用机制**
   ```javascript
   // Vue源码中的sameVnode函数
   function sameVnode(a, b) {
     return (
       a.key === b.key &&
       a.tag === b.tag &&
       a.isComment === b.isComment &&
       isDef(a.data) === isDef(b.data) &&
       sameInputType(a, b)
     )
   }
   ```

**具体应用场景：**

1. **列表渲染优化**
   ```javascript
   // 不使用key的问题
   <li v-for="item in list">{{ item.name }}</li>
   // 当list顺序改变时，所有li都会重新渲染

   // 使用key优化
   <li v-for="item in list" :key="item.id">{{ item.name }}</li>
   // 只有位置改变的节点会移动，内容不变的节点会复用
   ```

2. **强制替换元素**
   ```javascript
   // 使用key强制重新渲染组件
   <my-component :key="componentKey" />
   // 改变componentKey值可以强制组件重新创建
   ```

**性能影响分析：**

1. **使用正确key的优势**
   - 减少DOM操作：相同key的节点直接复用
   - 保持组件状态：避免不必要的组件重新创建
   - 提升渲染性能：减少虚拟DOM的创建和销毁

2. **使用错误key的问题**
   - 使用index作为key在列表项顺序改变时会导致性能问题
   - 重复key会导致渲染错误和状态混乱
   - 动态key频繁变化会失去复用优势

**最佳实践：**

1. **选择稳定唯一的标识**
   ```javascript
   // ✅ 推荐：使用数据的唯一标识
   <item v-for="user in users" :key="user.id" />

   // ❌ 避免：使用数组索引（在列表会变化的情况下）
   <item v-for="(user, index) in users" :key="index" />
   ```

2. **特殊场景处理**
   ```javascript
   // 当没有唯一id时，可以组合多个字段
   <item v-for="item in items" :key="`${item.type}-${item.name}`" />

   // 或者使用Symbol保证唯一性
   items.forEach(item => {
     if (!item._key) item._key = Symbol()
   })
   ```

**常见面试陷阱：**
- key的作用不仅仅是性能优化，更重要的是保证渲染的正确性
- 不是所有情况下都需要key，静态列表可以不使用
- key必须在同一层级中保持唯一，不同层级可以重复
4. v-for为什么要使用key，如果使用index会有什么问题

**为什么v-for需要key：**

1. **diff算法优化需求**
   - Vue的diff算法采用"就地更新"策略
   - 没有key时，Vue会尽可能复用现有DOM元素
   - 当列表顺序改变时，会导致不必要的DOM操作和状态错乱

2. **节点识别机制**
   ```javascript
   // 没有key的情况下，Vue只能通过位置来判断节点
   // 这会导致错误的节点复用
   [A, B, C] → [B, C, A]
   // Vue会认为：位置0的A变成了B，位置1的B变成了C，位置2的C变成了A
   // 实际上应该是：A移动到末尾，B和C前移
   ```

**使用index作为key的问题：**

1. **性能问题**
   ```javascript
   // 问题示例
   <li v-for="(item, index) in list" :key="index">
     <input v-model="item.value" />
     {{ item.name }}
   </li>

   // 当在数组头部插入新元素时：
   list.unshift({name: 'new', value: ''})

   // 所有元素的index都会改变：
   // 原来 index=0 的元素现在 index=1
   // 原来 index=1 的元素现在 index=2
   // Vue会认为所有元素都发生了变化，导致全部重新渲染
   ```

2. **状态混乱问题**
   ```javascript
   // 具体场景：待办事项列表
   const todos = [
     {id: 1, text: '学习Vue', done: false},
     {id: 2, text: '写代码', done: true},
     {id: 3, text: '休息', done: false}
   ]

   // 使用index作为key
   <div v-for="(todo, index) in todos" :key="index">
     <input type="checkbox" v-model="todo.done" />
     <span>{{ todo.text }}</span>
   </div>

   // 当删除第一项时，checkbox的状态会错位：
   // 原本"写代码"的勾选状态会出现在"学习Vue"上
   ```

**深层原理分析：**

1. **虚拟DOM复用逻辑**
   ```javascript
   // Vue的updateChildren函数中的逻辑
   function updateChildren(parentElm, oldCh, newCh) {
     // 当使用index作为key时
     // oldCh[0].key = 0, newCh[0].key = 0
     // Vue认为这是同一个节点，会进行patch操作
     // 但实际上内容完全不同，导致不必要的DOM操作
   }
   ```

2. **DOM操作对比**
   ```javascript
   // 使用唯一ID作为key
   // 插入操作：只需要创建1个新DOM节点
   // 移动操作：只需要移动DOM位置

   // 使用index作为key
   // 插入操作：需要更新所有后续节点的内容
   // 移动操作：需要更新多个节点的内容
   ```

**正确的解决方案：**

1. **使用稳定的唯一标识**
   ```javascript
   // ✅ 正确做法
   <li v-for="item in list" :key="item.id">
     <input v-model="item.value" />
     {{ item.name }}
   </li>

   // 即使列表顺序改变，每个item的id不变
   // Vue能正确识别和复用对应的DOM节点
   ```

2. **没有唯一ID时的处理**
   ```javascript
   // 方案1：生成唯一标识
   list.forEach((item, index) => {
     if (!item._uid) {
       item._uid = Date.now() + Math.random()
     }
   })

   // 方案2：使用内容hash（适用于不可变数据）
   <li v-for="item in list" :key="JSON.stringify(item)">
   ```

**性能测试对比：**
- 使用正确key：O(n) 时间复杂度，只需移动节点
- 使用index作为key：O(n²) 时间复杂度，需要更新多个节点内容
- 在大列表操作中，性能差异可达数十倍

**面试要点总结：**
- key的本质是为了让Vue能正确识别节点的身份
- index作为key在列表变化时会失去节点身份的稳定性
- 正确的key选择是性能优化和功能正确性的基础
5. 讲讲Keep-Alive

**核心概念：**
Keep-Alive是Vue的内置抽象组件，用于缓存不活动的组件实例，避免重复渲染，主要解决组件切换时的性能问题和状态保持需求。

**底层实现原理：**

1. **抽象组件特性**
   ```javascript
   // Keep-Alive组件定义
   export default {
     name: 'keep-alive',
     abstract: true, // 抽象组件，不会渲染DOM

     props: {
       include: patternTypes, // 缓存白名单
       exclude: patternTypes, // 缓存黑名单
       max: [String, Number]   // 最大缓存数量
     }
   }
   ```

2. **缓存机制**
   ```javascript
   // 核心缓存逻辑
   render() {
     const slot = this.$slots.default
     const vnode = getFirstComponentChild(slot)

     if (vnode) {
       const name = getComponentName(vnode.componentOptions)
       const { include, exclude } = this

       // 检查是否需要缓存
       if ((include && !matches(include, name)) ||
           (exclude && matches(exclude, name))) {
         return vnode
       }

       const { cache, keys } = this
       const key = vnode.key == null
         ? vnode.componentOptions.Ctor.cid + (vnode.componentOptions.tag || '')
         : vnode.key

       // 缓存命中
       if (cache[key]) {
         vnode.componentInstance = cache[key].componentInstance
         // LRU策略：将key移到末尾
         remove(keys, key)
         keys.push(key)
       } else {
         // 新增缓存
         cache[key] = vnode
         keys.push(key)

         // 超出最大缓存数量时，删除最久未使用的
         if (this.max && keys.length > parseInt(this.max)) {
           pruneCacheEntry(cache, keys[0], keys, this._vnode)
         }
       }

       vnode.data.keepAlive = true
     }
     return vnode || (slot && slot[0])
   }
   ```

**生命周期变化：**

1. **新增的生命周期钩子**
   ```javascript
   // 组件被激活时调用
   activated() {
     console.log('组件被激活')
     // 适合恢复数据、重新订阅事件等
   }

   // 组件被停用时调用
   deactivated() {
     console.log('组件被停用')
     // 适合暂停定时器、取消订阅等
   }
   ```

2. **生命周期执行顺序**
   ```
   首次进入：created → mounted → activated
   再次进入：activated（不会触发created和mounted）
   离开：deactivated（不会触发beforeDestroy和destroyed）
   ```

**使用方式和配置：**

1. **基础用法**
   ```javascript
   // 缓存所有组件
   <keep-alive>
     <router-view />
   </keep-alive>

   // 动态组件缓存
   <keep-alive>
     <component :is="currentComponent" />
   </keep-alive>
   ```

2. **条件缓存**
   ```javascript
   // include：只缓存指定组件
   <keep-alive include="ComponentA,ComponentB">
     <router-view />
   </keep-alive>

   // exclude：排除指定组件
   <keep-alive exclude="ComponentC">
     <router-view />
   </keep-alive>

   // 使用正则表达式
   <keep-alive :include="/^Component[AB]$/">
     <router-view />
   </keep-alive>

   // 限制缓存数量
   <keep-alive :max="10">
     <router-view />
   </keep-alive>
   ```

**LRU缓存策略：**

1. **算法原理**
   - Least Recently Used：最近最少使用
   - 当缓存满时，删除最久未访问的组件
   - 使用keys数组维护访问顺序

2. **实现细节**
   ```javascript
   // 访问时更新顺序
   if (cache[key]) {
     // 移除旧位置
     remove(keys, key)
     // 添加到末尾（最新访问）
     keys.push(key)
   }

   // 超出限制时删除最旧的
   if (this.max && keys.length > parseInt(this.max)) {
     pruneCacheEntry(cache, keys[0], keys, this._vnode)
   }
   ```

**性能优化效果：**

1. **避免重复渲染**
   - 组件实例被缓存，切换时不会重新创建
   - 保持组件状态，如表单输入、滚动位置等
   - 减少初始化开销，提升用户体验

2. **内存管理**
   - 通过max属性控制缓存数量，防止内存泄漏
   - LRU策略确保常用组件优先保留
   - 及时清理不需要的组件实例

**常见应用场景：**
- 路由切换时保持页面状态
- Tab页签切换
- 列表页和详情页的来回切换
- 复杂表单的临时保存

**注意事项：**
- 被缓存的组件不会触发beforeDestroy和destroyed
- 需要在deactivated中清理定时器等资源
- 组件name属性必须设置，用于include/exclude匹配
- 缓存的是组件实例，不是DOM元素

## Vue响应式系统

6. Proxy相对于Object.defineProperty的提升

**核心差异概述：**
Proxy是ES6新增的元编程特性，相比Object.defineProperty提供了更强大的拦截能力和更好的性能表现，这是Vue3选择Proxy作为响应式系统基础的主要原因。

**功能层面的提升：**

1. **拦截操作的完整性**
   ```javascript
   // Object.defineProperty只能拦截属性的get/set
   Object.defineProperty(obj, 'prop', {
     get() { return value },
     set(newVal) { value = newVal }
   })

   // Proxy可以拦截13种操作
   const proxy = new Proxy(target, {
     get(target, key, receiver) {},           // 属性读取
     set(target, key, value, receiver) {},    // 属性设置
     has(target, key) {},                     // in操作符
     deleteProperty(target, key) {},          // delete操作
     ownKeys(target) {},                      // Object.keys()
     getOwnPropertyDescriptor(target, key) {}, // Object.getOwnPropertyDescriptor()
     defineProperty(target, key, descriptor) {}, // Object.defineProperty()
     preventExtensions(target) {},            // Object.preventExtensions()
     getPrototypeOf(target) {},              // Object.getPrototypeOf()
     isExtensible(target) {},                // Object.isExtensible()
     setPrototypeOf(target, prototype) {},   // Object.setPrototypeOf()
     apply(target, thisArg, argumentsList) {}, // 函数调用
     construct(target, argumentsList, newTarget) {} // new操作
   })
   ```

2. **数组监听能力**
   ```javascript
   // Object.defineProperty对数组的局限性
   const arr = [1, 2, 3]
   // 无法监听数组长度变化
   arr.length = 0 // 无法检测
   // 无法监听数组索引的添加
   arr[3] = 4 // 无法检测

   // Vue2需要重写数组方法
   const arrayProto = Array.prototype
   const arrayMethods = Object.create(arrayProto)
   ;['push', 'pop', 'shift', 'unshift', 'splice', 'sort', 'reverse']
   .forEach(method => {
     arrayMethods[method] = function(...args) {
       const result = arrayProto[method].apply(this, args)
       // 手动触发更新
       notify()
       return result
     }
   })

   // Proxy天然支持数组监听
   const proxyArr = new Proxy(arr, {
     set(target, key, value) {
       console.log(`设置 ${key} = ${value}`)
       target[key] = value
       return true
     }
   })
   proxyArr.length = 0 // 可以检测
   proxyArr[3] = 4     // 可以检测
   ```

**性能层面的提升：**

1. **初始化性能**
   ```javascript
   // Object.defineProperty需要递归遍历所有属性
   function defineReactive(obj) {
     Object.keys(obj).forEach(key => {
       let val = obj[key]
       // 递归处理嵌套对象
       if (typeof val === 'object') {
         defineReactive(val)
       }
       Object.defineProperty(obj, key, {
         get() { return val },
         set(newVal) {
           if (typeof newVal === 'object') {
             defineReactive(newVal) // 新对象也需要递归处理
           }
           val = newVal
         }
       })
     })
   }

   // Proxy只需要在访问时才进行代理（懒代理）
   function reactive(obj) {
     return new Proxy(obj, {
       get(target, key) {
         const result = target[key]
         // 只有在访问时才代理子对象
         if (typeof result === 'object' && result !== null) {
           return reactive(result)
         }
         return result
       },
       set(target, key, value) {
         target[key] = value
         return true
       }
     })
   }
   ```

2. **运行时性能**
   ```javascript
   // 性能测试对比（伪代码）
   const obj = { /* 1000个属性的对象 */ }

   // Object.defineProperty方式
   console.time('defineProperty')
   defineReactive(obj) // 需要遍历所有属性
   console.timeEnd('defineProperty') // ~10ms

   // Proxy方式
   console.time('proxy')
   const proxyObj = reactive(obj) // 只创建一个代理
   console.timeEnd('proxy') // ~0.1ms
   ```

**动态属性处理：**

1. **新增属性的监听**
   ```javascript
   // Object.defineProperty无法监听新增属性
   const obj = { a: 1 }
   defineReactive(obj)
   obj.b = 2 // 无法检测到，Vue2需要使用Vue.set()

   // Proxy天然支持新增属性监听
   const proxyObj = new Proxy(obj, {
     set(target, key, value) {
       console.log(`新增/修改属性: ${key} = ${value}`)
       target[key] = value
       return true
     }
   })
   proxyObj.b = 2 // 可以检测到
   ```

2. **属性删除的监听**
   ```javascript
   // Object.defineProperty无法监听属性删除
   delete obj.a // 无法检测，Vue2需要使用Vue.delete()

   // Proxy支持删除监听
   const proxyObj = new Proxy(obj, {
     deleteProperty(target, key) {
       console.log(`删除属性: ${key}`)
       delete target[key]
       return true
     }
   })
   delete proxyObj.a // 可以检测到
   ```

**内存占用对比：**

1. **Object.defineProperty**
   - 每个属性都需要创建getter/setter
   - 需要保存原始值的引用
   - 深层对象需要递归处理，内存占用较大

2. **Proxy**
   - 只需要一个Proxy实例
   - 懒代理机制，按需创建子代理
   - 内存占用更少，特别是对于大型对象

**兼容性考虑：**

1. **浏览器支持**
   - Object.defineProperty：IE9+
   - Proxy：IE不支持，现代浏览器支持
   - 这是Vue3不支持IE的主要原因

2. **Polyfill可能性**
   - Object.defineProperty可以部分polyfill
   - Proxy无法完全polyfill，因为需要引擎层面支持

**Vue3中的实际应用：**
```javascript
// Vue3响应式系统的核心实现
function createReactiveObject(target, isReadonly, baseHandlers) {
  return new Proxy(target, baseHandlers)
}

const mutableHandlers = {
  get: createGetter(),
  set: createSetter(),
  deleteProperty,
  has,
  ownKeys
}
```

**总结：**
Proxy相对于Object.defineProperty的提升主要体现在功能完整性、性能优化、代码简洁性三个方面，这些优势使得Vue3的响应式系统更加强大和高效。
7. vue2和vue3的差别，详细介绍响应式原理、diff算法

**整体架构差异：**

1. **编程范式变化**
   - Vue2：Options API为主，基于对象配置
   - Vue3：Composition API为主，基于函数组合，更好的TypeScript支持

2. **构建系统**
   - Vue2：基于Rollup，单一构建产物
   - Vue3：基于Rollup + esbuild，支持Tree-shaking，模块化构建

**响应式原理对比：**

1. **Vue2响应式系统**
   ```javascript
   // 核心实现：Object.defineProperty + 观察者模式
   function defineReactive(obj, key, val) {
     const dep = new Dep() // 每个属性对应一个依赖收集器

     Object.defineProperty(obj, key, {
       enumerable: true,
       configurable: true,
       get: function reactiveGetter() {
         // 依赖收集
         if (Dep.target) {
           dep.depend()
         }
         return val
       },
       set: function reactiveSetter(newVal) {
         if (newVal === val) return
         val = newVal
         // 派发更新
         dep.notify()
       }
     })
   }

   // 依赖收集器
   class Dep {
     constructor() {
       this.subs = []
     }

     depend() {
       if (Dep.target) {
         this.subs.push(Dep.target)
       }
     }

     notify() {
       this.subs.forEach(watcher => watcher.update())
     }
   }

   // 观察者
   class Watcher {
     constructor(vm, expOrFn, cb) {
       this.vm = vm
       this.cb = cb
       this.getter = expOrFn
       this.value = this.get()
     }

     get() {
       Dep.target = this // 设置当前watcher
       const value = this.getter.call(this.vm)
       Dep.target = null
       return value
     }

     update() {
       this.cb.call(this.vm)
     }
   }
   ```

2. **Vue3响应式系统**
   ```javascript
   // 核心实现：Proxy + effect函数
   const targetMap = new WeakMap() // 存储依赖关系

   function reactive(target) {
     return new Proxy(target, {
       get(target, key, receiver) {
         const result = Reflect.get(target, key, receiver)
         // 依赖收集
         track(target, key)
         // 如果是对象，递归代理
         if (isObject(result)) {
           return reactive(result)
         }
         return result
       },

       set(target, key, value, receiver) {
         const oldValue = target[key]
         const result = Reflect.set(target, key, value, receiver)
         // 派发更新
         if (oldValue !== value) {
           trigger(target, key)
         }
         return result
       }
     })
   }

   // 依赖收集
   function track(target, key) {
     if (!activeEffect) return

     let depsMap = targetMap.get(target)
     if (!depsMap) {
       targetMap.set(target, (depsMap = new Map()))
     }

     let dep = depsMap.get(key)
     if (!dep) {
       depsMap.set(key, (dep = new Set()))
     }

     dep.add(activeEffect)
   }

   // 派发更新
   function trigger(target, key) {
     const depsMap = targetMap.get(target)
     if (!depsMap) return

     const dep = depsMap.get(key)
     if (dep) {
       dep.forEach(effect => effect())
     }
   }

   // effect函数
   let activeEffect = null
   function effect(fn) {
     const effectFn = () => {
       activeEffect = effectFn
       fn()
       activeEffect = null
     }
     effectFn()
     return effectFn
   }
   ```

**Diff算法对比：**

1. **Vue2 Diff算法**
   ```javascript
   // 双端比较算法
   function updateChildren(parentElm, oldCh, newCh) {
     let oldStartIdx = 0, newStartIdx = 0
     let oldEndIdx = oldCh.length - 1
     let newEndIdx = newCh.length - 1
     let oldStartVnode = oldCh[0]
     let oldEndVnode = oldCh[oldEndIdx]
     let newStartVnode = newCh[0]
     let newEndVnode = newCh[newEndIdx]

     while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {
       if (isUndef(oldStartVnode)) {
         oldStartVnode = oldCh[++oldStartIdx]
       } else if (isUndef(oldEndVnode)) {
         oldEndVnode = oldCh[--oldEndIdx]
       } else if (sameVnode(oldStartVnode, newStartVnode)) {
         // 头头比较
         patchVnode(oldStartVnode, newStartVnode)
         oldStartVnode = oldCh[++oldStartIdx]
         newStartVnode = newCh[++newStartIdx]
       } else if (sameVnode(oldEndVnode, newEndVnode)) {
         // 尾尾比较
         patchVnode(oldEndVnode, newEndVnode)
         oldEndVnode = oldCh[--oldEndIdx]
         newEndVnode = newCh[--newEndIdx]
       } else if (sameVnode(oldStartVnode, newEndVnode)) {
         // 头尾比较
         patchVnode(oldStartVnode, newEndVnode)
         nodeOps.insertBefore(parentElm, oldStartVnode.elm, nodeOps.nextSibling(oldEndVnode.elm))
         oldStartVnode = oldCh[++oldStartIdx]
         newEndVnode = newCh[--newEndIdx]
       } else if (sameVnode(oldEndVnode, newStartVnode)) {
         // 尾头比较
         patchVnode(oldEndVnode, newStartVnode)
         nodeOps.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm)
         oldEndVnode = oldCh[--oldEndIdx]
         newStartVnode = newCh[++newStartIdx]
       } else {
         // 都不匹配，通过key查找
         const idxInOld = findIdxInOld(newStartVnode, oldCh, oldStartIdx, oldEndIdx)
         if (isUndef(idxInOld)) {
           createElm(newStartVnode, parentElm, oldStartVnode.elm)
         } else {
           const vnodeToMove = oldCh[idxInOld]
           if (sameVnode(vnodeToMove, newStartVnode)) {
             patchVnode(vnodeToMove, newStartVnode)
             oldCh[idxInOld] = undefined
             nodeOps.insertBefore(parentElm, vnodeToMove.elm, oldStartVnode.elm)
           } else {
             createElm(newStartVnode, parentElm, oldStartVnode.elm)
           }
         }
         newStartVnode = newCh[++newStartIdx]
       }
     }
   }
   ```

2. **Vue3 Diff算法（快速Diff）**
   ```javascript
   // 最长递增子序列 + 预处理优化
   function patchKeyedChildren(c1, c2, container) {
     let i = 0
     const l2 = c2.length
     let e1 = c1.length - 1
     let e2 = l2 - 1

     // 1. 从头开始比较
     while (i <= e1 && i <= e2) {
       const n1 = c1[i]
       const n2 = c2[i]
       if (isSameVNodeType(n1, n2)) {
         patch(n1, n2, container)
       } else {
         break
       }
       i++
     }

     // 2. 从尾开始比较
     while (i <= e1 && i <= e2) {
       const n1 = c1[e1]
       const n2 = c2[e2]
       if (isSameVNodeType(n1, n2)) {
         patch(n1, n2, container)
       } else {
         break
       }
       e1--
       e2--
     }

     // 3. 新增节点
     if (i > e1) {
       if (i <= e2) {
         const nextPos = e2 + 1
         const anchor = nextPos < l2 ? c2[nextPos].el : null
         while (i <= e2) {
           patch(null, c2[i], container, anchor)
           i++
         }
       }
     }
     // 4. 删除节点
     else if (i > e2) {
       while (i <= e1) {
         unmount(c1[i])
         i++
       }
     }
     // 5. 复杂情况：使用最长递增子序列
     else {
       const s1 = i
       const s2 = i

       // 构建新节点的key -> index映射
       const keyToNewIndexMap = new Map()
       for (i = s2; i <= e2; i++) {
         const nextChild = c2[i]
         if (nextChild.key != null) {
           keyToNewIndexMap.set(nextChild.key, i)
         }
       }

       // 构建新旧节点的索引映射
       const newIndexToOldIndexMap = new Array(e2 - s2 + 1)
       for (i = 0; i < newIndexToOldIndexMap.length; i++) {
         newIndexToOldIndexMap[i] = 0
       }

       let moved = false
       let maxNewIndexSoFar = 0

       for (i = s1; i <= e1; i++) {
         const prevChild = c1[i]
         const newIndex = keyToNewIndexMap.get(prevChild.key)

         if (newIndex === undefined) {
           unmount(prevChild)
         } else {
           newIndexToOldIndexMap[newIndex - s2] = i + 1
           if (newIndex >= maxNewIndexSoFar) {
             maxNewIndexSoFar = newIndex
           } else {
             moved = true
           }
           patch(prevChild, c2[newIndex], container)
         }
       }

       // 计算最长递增子序列
       const increasingNewIndexSequence = moved
         ? getSequence(newIndexToOldIndexMap)
         : []

       // 移动和挂载节点
       let j = increasingNewIndexSequence.length - 1
       for (i = newIndexToOldIndexMap.length - 1; i >= 0; i--) {
         const nextIndex = s2 + i
         const nextChild = c2[nextIndex]
         const anchor = nextIndex + 1 < l2 ? c2[nextIndex + 1].el : null

         if (newIndexToOldIndexMap[i] === 0) {
           // 新增节点
           patch(null, nextChild, container, anchor)
         } else if (moved) {
           if (j < 0 || i !== increasingNewIndexSequence[j]) {
             // 移动节点
             move(nextChild, container, anchor)
           } else {
             j--
           }
         }
       }
     }
   }
   ```

**Diff算法性能对比：**

1. **Vue2双端比较**
   - 时间复杂度：O(n)
   - 空间复杂度：O(1)
   - 优势：简单直观，内存占用少
   - 劣势：在复杂场景下移动次数较多

2. **Vue3快速Diff**
   - 时间复杂度：O(n log n)（最长递增子序列）
   - 空间复杂度：O(n)
   - 优势：移动次数最少，性能更优
   - 劣势：算法复杂度高，内存占用稍多

**其他重要差异：**

1. **编译优化**
   ```javascript
   // Vue2编译结果
   function render() {
     return h('div', [
       h('span', this.msg),
       h('p', this.desc)
     ])
   }

   // Vue3编译结果（静态提升 + 补丁标记）
   const _hoisted_1 = { class: "static" }
   function render() {
     return (openBlock(), createElementBlock("div", null, [
       createElementVNode("span", _hoisted_1, toDisplayString(_ctx.msg), 1 /* TEXT */),
       createElementVNode("p", null, toDisplayString(_ctx.desc), 1 /* TEXT */)
     ]))
   }
   ```

2. **Tree-shaking支持**
   - Vue2：整体导入，无法tree-shake
   - Vue3：模块化设计，支持按需导入

3. **TypeScript支持**
   - Vue2：通过vue-class-component等插件支持
   - Vue3：原生TypeScript支持，类型推导更好

4. **Fragment支持**
   - Vue2：组件必须有单一根节点
   - Vue3：支持多根节点（Fragment）

5. **Teleport和Suspense**
   - Vue2：无原生支持
   - Vue3：内置Teleport（传送门）和Suspense（异步组件）

**性能提升总结：**
- 响应式系统：Proxy比Object.defineProperty性能提升约2-3倍
- 编译优化：静态提升和补丁标记减少50%的运行时开销
- Diff算法：复杂场景下移动操作减少60-80%
- 包体积：支持tree-shaking，按需引入可减少40%体积

**迁移注意事项：**
- 破坏性变更：全局API、事件API、过滤器移除等
- 兼容性：Vue3不支持IE，需要考虑浏览器兼容性
- 生态系统：部分Vue2插件需要升级或替换
8. 讲讲Diff算法

**Diff算法核心概念：**
Diff算法是虚拟DOM的核心，用于比较新旧虚拟DOM树的差异，并以最小的代价更新真实DOM。其本质是一个树的比较算法，但通过一系列优化策略将O(n³)的复杂度降低到O(n)。

**传统树Diff的问题：**
```javascript
// 传统的树比较算法需要：
// 1. 遍历树1的每个节点
// 2. 遍历树2的每个节点进行比较
// 3. 计算最小编辑距离
// 时间复杂度：O(n³)，对于1000个节点需要10亿次比较
```

**Vue Diff算法的优化策略：**

1. **同层比较策略**
   ```javascript
   // 只比较同一层级的节点，不跨层级比较
   // 基于Web应用中跨层级移动DOM的情况很少这一假设

   function patch(oldVnode, newVnode) {
     // 1. 节点类型不同，直接替换
     if (!sameVnode(oldVnode, newVnode)) {
       return replaceVnode(oldVnode, newVnode)
     }

     // 2. 节点类型相同，比较属性和子节点
     patchVnode(oldVnode, newVnode)
   }

   function sameVnode(a, b) {
     return (
       a.key === b.key &&           // key相同
       a.tag === b.tag &&           // 标签相同
       a.isComment === b.isComment && // 注释节点标识相同
       isDef(a.data) === isDef(b.data) && // 都有或都没有data
       sameInputType(a, b)          // input类型相同
     )
   }
   ```

2. **节点复用策略**
   ```javascript
   function patchVnode(oldVnode, newVnode) {
     // 完全相同的节点，直接返回
     if (oldVnode === newVnode) return

     const elm = newVnode.elm = oldVnode.elm

     // 更新属性
     updateAttrs(oldVnode, newVnode)
     updateClass(oldVnode, newVnode)
     updateStyle(oldVnode, newVnode)
     updateProps(oldVnode, newVnode)
     updateListeners(oldVnode, newVnode)

     const oldCh = oldVnode.children
     const newCh = newVnode.children

     // 子节点比较
     if (isUndef(newVnode.text)) {
       if (isDef(oldCh) && isDef(newCh)) {
         // 都有子节点，进行子节点diff
         if (oldCh !== newCh) updateChildren(elm, oldCh, newCh)
       } else if (isDef(newCh)) {
         // 只有新节点有子节点，添加子节点
         if (isDef(oldVnode.text)) nodeOps.setTextContent(elm, '')
         addVnodes(elm, null, newCh, 0, newCh.length - 1)
       } else if (isDef(oldCh)) {
         // 只有旧节点有子节点，删除子节点
         removeVnodes(elm, oldCh, 0, oldCh.length - 1)
       } else if (isDef(oldVnode.text)) {
         // 都没有子节点，清空文本
         nodeOps.setTextContent(elm, '')
       }
     } else if (oldVnode.text !== newVnode.text) {
       // 文本节点更新
       nodeOps.setTextContent(elm, newVnode.text)
     }
   }
   ```

**双端比较算法详解：**

1. **四种比较策略**
   ```javascript
   function updateChildren(parentElm, oldCh, newCh) {
     let oldStartIdx = 0, newStartIdx = 0
     let oldEndIdx = oldCh.length - 1
     let newEndIdx = newCh.length - 1
     let oldStartVnode = oldCh[0]
     let oldEndVnode = oldCh[oldEndIdx]
     let newStartVnode = newCh[0]
     let newEndVnode = newCh[newEndIdx]
     let oldKeyToIdx, idxInOld, vnodeToMove, refElm

     while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {
       if (isUndef(oldStartVnode)) {
         oldStartVnode = oldCh[++oldStartIdx] // 跳过已处理的节点
       } else if (isUndef(oldEndVnode)) {
         oldEndVnode = oldCh[--oldEndIdx]
       } else if (sameVnode(oldStartVnode, newStartVnode)) {
         // 策略1：头头比较 - 最常见的情况
         patchVnode(oldStartVnode, newStartVnode)
         oldStartVnode = oldCh[++oldStartIdx]
         newStartVnode = newCh[++newStartIdx]
       } else if (sameVnode(oldEndVnode, newEndVnode)) {
         // 策略2：尾尾比较 - 尾部添加的情况
         patchVnode(oldEndVnode, newEndVnode)
         oldEndVnode = oldCh[--oldEndIdx]
         newEndVnode = newCh[--newEndIdx]
       } else if (sameVnode(oldStartVnode, newEndVnode)) {
         // 策略3：头尾比较 - 节点向后移动
         patchVnode(oldStartVnode, newEndVnode)
         nodeOps.insertBefore(parentElm, oldStartVnode.elm, nodeOps.nextSibling(oldEndVnode.elm))
         oldStartVnode = oldCh[++oldStartIdx]
         newEndVnode = newCh[--newEndIdx]
       } else if (sameVnode(oldEndVnode, newStartVnode)) {
         // 策略4：尾头比较 - 节点向前移动
         patchVnode(oldEndVnode, newStartVnode)
         nodeOps.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm)
         oldEndVnode = oldCh[--oldEndIdx]
         newStartVnode = newCh[++newStartIdx]
       } else {
         // 都不匹配，通过key查找
         if (isUndef(oldKeyToIdx)) {
           oldKeyToIdx = createKeyToOldIdx(oldCh, oldStartIdx, oldEndIdx)
         }
         idxInOld = isDef(newStartVnode.key)
           ? oldKeyToIdx[newStartVnode.key]
           : findIdxInOld(newStartVnode, oldCh, oldStartIdx, oldEndIdx)

         if (isUndef(idxInOld)) {
           // 新节点，创建并插入
           createElm(newStartVnode, insertedVnodeQueue, parentElm, oldStartVnode.elm, false, newCh, newStartIdx)
         } else {
           vnodeToMove = oldCh[idxInOld]
           if (sameVnode(vnodeToMove, newStartVnode)) {
             // 找到相同节点，移动位置
             patchVnode(vnodeToMove, newStartVnode)
             oldCh[idxInOld] = undefined
             nodeOps.insertBefore(parentElm, vnodeToMove.elm, oldStartVnode.elm)
           } else {
             // key相同但节点不同，创建新节点
             createElm(newStartVnode, insertedVnodeQueue, parentElm, oldStartVnode.elm, false, newCh, newStartIdx)
           }
         }
         newStartVnode = newCh[++newStartIdx]
       }
     }

     // 处理剩余节点
     if (oldStartIdx > oldEndIdx) {
       // 添加新节点
       refElm = isUndef(newCh[newEndIdx + 1]) ? null : newCh[newEndIdx + 1].elm
       addVnodes(parentElm, refElm, newCh, newStartIdx, newEndIdx, insertedVnodeQueue)
     } else if (newStartIdx > newEndIdx) {
       // 删除旧节点
       removeVnodes(parentElm, oldCh, oldStartIdx, oldEndIdx)
     }
   }
   ```

2. **Key的重要性**
   ```javascript
   // 创建key到索引的映射
   function createKeyToOldIdx(children, beginIdx, endIdx) {
     let i, key
     const map = {}
     for (i = beginIdx; i <= endIdx; ++i) {
       key = children[i].key
       if (isDef(key)) map[key] = i
     }
     return map
   }

   // 没有key时的线性查找
   function findIdxInOld(node, oldCh, start, end) {
     for (let i = start; i < end; i++) {
       const c = oldCh[i]
       if (isDef(c) && sameVnode(node, c)) return i
     }
   }
   ```

**算法优化效果：**

1. **时间复杂度分析**
   ```javascript
   // 最好情况：O(n) - 所有节点都能通过头头或尾尾比较匹配
   // 最坏情况：O(n²) - 每个新节点都需要在旧节点中线性查找
   // 平均情况：O(n) - 大多数Web应用的实际表现
   ```

2. **空间复杂度**
   ```javascript
   // O(n) - 主要是key到索引的映射表
   ```

**实际应用场景分析：**

1. **列表头部插入**
   ```javascript
   // 旧：[A, B, C]
   // 新：[D, A, B, C]
   // 双端比较：4次比较 + 1次DOM插入
   // 传统比较：需要移动所有节点
   ```

2. **列表尾部插入**
   ```javascript
   // 旧：[A, B, C]
   // 新：[A, B, C, D]
   // 双端比较：通过尾尾比较，只需1次DOM插入
   ```

3. **列表中间插入**
   ```javascript
   // 旧：[A, B, C]
   // 新：[A, D, B, C]
   // 双端比较：通过key查找，最小化DOM操作
   ```

**性能优化建议：**
- 合理使用key，避免使用index作为key
- 减少跨层级的DOM移动
- 尽量保持列表结构的稳定性
- 对于大列表考虑虚拟滚动等技术

**总结：**
Vue的Diff算法通过同层比较、双端比较、key优化等策略，将复杂的树比较问题简化为线性问题，在保证功能正确性的同时大幅提升了性能。

## Vue特殊机制

9. nextTick作用与使用场景

**核心作用：**
nextTick是Vue提供的一个异步API，用于在下次DOM更新循环结束之后执行延迟回调。它解决了数据更新后立即操作DOM时，DOM还未更新完成的问题。

**底层实现原理：**

1. **异步更新队列机制**
   ```javascript
   // Vue的更新策略：异步批量更新
   let pending = false
   const callbacks = []

   function flushCallbacks() {
     pending = false
     const copies = callbacks.slice(0)
     callbacks.length = 0
     for (let i = 0; i < copies.length; i++) {
       copies[i]()
     }
   }

   // nextTick的核心实现
   export function nextTick(cb, ctx) {
     let _resolve
     callbacks.push(() => {
       if (cb) {
         try {
           cb.call(ctx)
         } catch (e) {
           handleError(e, ctx, 'nextTick')
         }
       } else if (_resolve) {
         _resolve(ctx)
       }
     })

     if (!pending) {
       pending = true
       timerFunc()
     }

     // 支持Promise
     if (!cb && typeof Promise !== 'undefined') {
       return new Promise(resolve => {
         _resolve = resolve
       })
     }
   }
   ```

2. **降级策略（timerFunc）**
   ```javascript
   let timerFunc

   // 优先级：Promise.then > MutationObserver > setImmediate > setTimeout
   if (typeof Promise !== 'undefined' && isNative(Promise)) {
     // 优先使用Promise.then（微任务）
     const p = Promise.resolve()
     timerFunc = () => {
       p.then(flushCallbacks)
       // iOS的UIWebView中Promise.then可能不会触发，需要强制刷新
       if (isIOS) setTimeout(noop)
     }
     isUsingMicroTask = true
   } else if (!isIE && typeof MutationObserver !== 'undefined' && (
     isNative(MutationObserver) ||
     MutationObserver.toString() === '[object MutationObserverConstructor]'
   )) {
     // 使用MutationObserver（微任务）
     let counter = 1
     const observer = new MutationObserver(flushCallbacks)
     const textNode = document.createTextNode(String(counter))
     observer.observe(textNode, {
       characterData: true
     })
     timerFunc = () => {
       counter = (counter + 1) % 2
       textNode.data = String(counter)
     }
     isUsingMicroTask = true
   } else if (typeof setImmediate !== 'undefined' && isNative(setImmediate)) {
     // 使用setImmediate（宏任务，但比setTimeout快）
     timerFunc = () => {
       setImmediate(flushCallbacks)
     }
   } else {
     // 最后降级到setTimeout（宏任务）
     timerFunc = () => {
       setTimeout(flushCallbacks, 0)
     }
   }
   ```

**使用场景详解：**

1. **DOM更新后的操作**
   ```javascript
   // 场景1：获取更新后的DOM元素尺寸
   export default {
     data() {
       return {
         message: 'Hello'
       }
     },
     methods: {
       updateMessage() {
         this.message = 'Hello Vue!'

         // 错误做法：DOM还未更新
         console.log(this.$el.textContent) // 输出：Hello

         // 正确做法：等待DOM更新
         this.$nextTick(() => {
           console.log(this.$el.textContent) // 输出：Hello Vue!
         })

         // 或者使用Promise语法
         this.$nextTick().then(() => {
           console.log(this.$el.textContent) // 输出：Hello Vue!
         })
       }
     }
   }
   ```

2. **动态组件渲染后的操作**
   ```javascript
   // 场景2：动态创建组件后获取其DOM
   methods: {
     async addComponent() {
       this.showComponent = true

       // 等待组件渲染完成
       await this.$nextTick()

       // 现在可以安全地操作新组件的DOM
       const newComponent = this.$refs.dynamicComponent
       newComponent.focus()
     }
   }
   ```

3. **表单验证场景**
   ```javascript
   // 场景3：表单数据更新后触发验证
   methods: {
     updateFormData() {
       this.formData.email = '<EMAIL>'

       this.$nextTick(() => {
         // 确保表单数据已更新到DOM后再验证
         this.$refs.form.validate()
       })
     }
   }
   ```

4. **第三方库集成**
   ```javascript
   // 场景4：集成第三方DOM操作库
   mounted() {
     this.$nextTick(() => {
       // 确保DOM完全渲染后初始化第三方库
       new Swiper(this.$refs.swiper, {
         // 配置项
       })
     })
   }
   ```

10. 讲讲NextTick，什么情况下会使用NextTick？

**NextTick的工作机制：**

1. **事件循环中的位置**
   ```javascript
   // 浏览器事件循环
   console.log('1') // 同步任务

   setTimeout(() => console.log('2'), 0) // 宏任务

   this.$nextTick(() => console.log('3')) // 微任务（通常）

   Promise.resolve().then(() => console.log('4')) // 微任务

   console.log('5') // 同步任务

   // 输出顺序：1 -> 5 -> 3 -> 4 -> 2
   ```

2. **批量更新机制**
   ```javascript
   // Vue的批量更新示例
   methods: {
     batchUpdate() {
       // 这些操作会被批量处理，只触发一次DOM更新
       this.count++
       this.count++
       this.count++

       console.log(this.$el.textContent) // 还是旧值

       this.$nextTick(() => {
         console.log(this.$el.textContent) // 新值（count + 3）
       })
     }
   }
   ```

**具体使用情况：**

1. **需要获取更新后DOM信息时**
   ```javascript
   // 获取元素高度、宽度、滚动位置等
   this.items.push(newItem)
   this.$nextTick(() => {
     const height = this.$refs.list.scrollHeight
     this.scrollToBottom()
   })
   ```

2. **需要操作动态渲染的元素时**
   ```javascript
   // v-if切换后操作元素
   this.showModal = true
   this.$nextTick(() => {
     this.$refs.modalInput.focus()
   })
   ```

3. **需要确保子组件完全渲染时**
   ```javascript
   // 父组件等待子组件渲染完成
   this.childData = newData
   this.$nextTick(() => {
     this.$refs.childComponent.doSomething()
   })
   ```

11. 纯前端有这个机制吗

**原生JavaScript中的类似机制：**

1. **setTimeout(fn, 0)**
   ```javascript
   // 最基础的异步执行方式
   console.log('1')
   setTimeout(() => console.log('2'), 0)
   console.log('3')
   // 输出：1 -> 3 -> 2

   // 但这是宏任务，执行时机较晚
   ```

2. **Promise.then()**
   ```javascript
   // 微任务，执行时机更早
   console.log('1')
   Promise.resolve().then(() => console.log('2'))
   console.log('3')
   // 输出：1 -> 3 -> 2

   // 这是Vue nextTick的首选实现方式
   ```

3. **MutationObserver**
   ```javascript
   // DOM变化监听，也是微任务
   const observer = new MutationObserver(() => {
     console.log('DOM changed')
   })

   const textNode = document.createTextNode('1')
   observer.observe(textNode, { characterData: true })

   textNode.data = '2' // 触发回调
   ```

4. **queueMicrotask()**
   ```javascript
   // 现代浏览器提供的微任务API
   console.log('1')
   queueMicrotask(() => console.log('2'))
   console.log('3')
   // 输出：1 -> 3 -> 2
   ```

**自实现nextTick机制：**

```javascript
// 简化版的nextTick实现
class SimpleNextTick {
  constructor() {
    this.callbacks = []
    this.pending = false
  }

  nextTick(callback) {
    return new Promise((resolve) => {
      this.callbacks.push(() => {
        if (callback) {
          try {
            callback()
          } catch (error) {
            console.error(error)
          }
        }
        resolve()
      })

      if (!this.pending) {
        this.pending = true
        this.timerFunc()
      }
    })
  }

  timerFunc() {
    // 优先使用微任务
    if (typeof Promise !== 'undefined') {
      Promise.resolve().then(this.flushCallbacks.bind(this))
    } else {
      setTimeout(this.flushCallbacks.bind(this), 0)
    }
  }

  flushCallbacks() {
    this.pending = false
    const copies = this.callbacks.slice(0)
    this.callbacks.length = 0
    copies.forEach(callback => callback())
  }
}

// 使用示例
const nextTick = new SimpleNextTick()

document.getElementById('btn').textContent = 'New Text'
nextTick.nextTick(() => {
  console.log('DOM updated:', document.getElementById('btn').textContent)
})
```

**React中的类似机制：**

```javascript
// React 18的并发特性
import { flushSync } from 'react-dom'

// 强制同步更新
flushSync(() => {
  setCount(count + 1)
})
// DOM立即更新

// 或者使用useEffect
useEffect(() => {
  // 类似nextTick的效果
  console.log('DOM updated')
}, [count])
```

**总结：**
纯前端确实有类似nextTick的机制，主要基于事件循环的微任务和宏任务。Vue的nextTick本质上是对这些原生API的封装和优化，提供了更好的开发体验和兼容性处理。

## 状态管理

12. vuex和pinia底层实现原理
13. 发布订阅模式
14. 为什么使用xx作为你的全局状态管理工具？之前有对比过其他方案吗？

## Vue项目实践

15. Vue2向Vue3框架迁移有使用AI Agent吗？为什么不用？
16. 在做新老框架迁移的时候有没有自己的SOP
