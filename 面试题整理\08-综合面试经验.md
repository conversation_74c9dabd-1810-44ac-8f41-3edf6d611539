# 综合面试经验题

## 自我介绍与背景

1. 【自我介绍】
2. 为什么现在在看机会？有什么考量？
3. 为什么选择前端
4. 怎么学习计算机的

## 项目经验

5. 【项目拷打】
6. 说说项目中最有挑战的事
7. 做过的最有挑战性的事
8. 做过的印象最深的事
9. 项目中有使用过AI吗，用AI做了哪些事
10. 项目中有使用过AI吗，平时有学习过AI相关原理吗
11. 如何理解LLM的底层原理，如何理解prompt

## 项目质量与用户体验

12. 你认为你们项目的用户满意度怎么样？怎么得出的？
13. 你认为判断用户满不满意有哪些指标？
14. 有多少用户在使用你们的软件？

## 个人发展与学习

15. 复盘一下你这一年的经历，有什么做得好或不好的地方（技术上、综合能力上等等）
16. 最近在学什么新知识
17. 最近学的一个知识点是什么，能和我讲讲吗
18. 认为自己有什么优点？
19. 你认为一个好的前端开发工程师应该具备哪些能力

## 团队协作与沟通

20. 有遇到过什么冲突、受到过什么委屈吗，怎么处理的
21. 和同事有过冲突吗，怎么解决的
22. 在开发过程中有遇到过什么难题吗
23. 有自己主动提出过去做一件事吗，如果有的话做了什么

## 绩效与表现

24. 之前的绩效怎么样？

## 反问环节

25. 【反问环节】
26. 反问
