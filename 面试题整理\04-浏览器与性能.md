# 浏览器与性能相关面试题

## 浏览器存储

1. 浏览器存储
2. 数据存储
3. cookie中通常会携带什么信息
4. 如何设置cookie的过期时间
5. 页面通信
6. 页面数据联动 sessionStorage

## 浏览器缓存

7. 浏览器缓存e-tag和last-modified的优先级
8. 什么情况下会使用304？判断304是在本地还是CDN？
9. 301、302、304状态码
10. 304状态码的请求头关键字

## 浏览器渲染

11. 浏览器访问到渲染

## 性能指标

12. Performance有哪些指标能够反映项目性能？
13. 计算FCP的开始和结束点分别是什么？浏览器是怎么拿到这个时间的？
14. 你们的项目性能评估采纳了哪些指标

## 性能监控与调试

15. 有遇到过一个网页在用户的电脑上打开很慢，但在你的电脑上很快的情况吗，怎么处理的
16. 你们的项目还是存在一些性能问题，你知道原因吗？

## Web Workers

17. 为什么要将拉取数据流放在Web Worker中？

## Canvas相关

18. 了解Canvas的一些底层实现原理吗
19. 在使用Canvas的时候遇到了什么坑，有没有自己写什么工具去优化
