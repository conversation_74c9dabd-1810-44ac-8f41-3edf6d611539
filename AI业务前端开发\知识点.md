2025 前端开发者「对话系统 + LLM/Agent」业务级学习索引  
（98 个知识点，含掌握程度 & 落地场景，按业务闭环分组）

————————————————————————————————  
Ⅰ. 对话输入层（人机交互）  
1  多轮指代消解（Coreference Resolution）  
 ⧫ 掌握程度：能调用开源包（如 @allenai/coref）并给出示例数据验证结果  
 ⧫ 落地场景：客服机器人自动补全“它”“这个”指代内容，减少用户重复输入  

2  实体跟踪（Entity Tracking）  
 ⧫ 掌握程度：在浏览器端维护 Entity Map，与后端 DST 对齐  
 ⧫ 落地场景：保险理赔对话中持续追踪“保单号”“出险时间”  

3  零指代/省略句补全（Zero Anaphora Recovery）  
 ⧫ 掌握程度：用规则+轻量模型补全，前端兜底提示  
 ⧫ 落地场景：口语化聊天“帮我续一下”→“帮我续一下会员”  

4  长距离依赖建模（Long-Distance Dependency）  
 ⧫ 掌握程度：理解 LLM 对长上文的支持边界，前端提示“已截断”  
 ⧫ 落地场景：长文档问答，用户翻到第 20 页仍关联第 3 页内容  

5  多候选消歧（Multi-Candidate Disambiguation）  
 ⧫ 掌握程度：前端高亮歧义词并弹出候选卡片  
 ⧫ 落地场景：地名歧义“长沙”→“长沙市/长沙县”  

6  对话状态管理（Dialogue State Tracking, DST）  
 ⧫ 掌握程度：能设计 Redux/Zustand store 与后端 state schema 同步  
 ⧫ 落地场景：机票多轮搜索“出发地→目的地→时间”  

7  提示词工程（Prompt Engineering）  
 ⧫ 掌握程度：编写可维护的 prompt 模板，支持变量注入  
 ⧫ 落地场景：营销文案生成器，用户输入品牌名一键出 10 条 slogan  

8  意图识别与槽位填充联合模型（Joint NLU）  
 ⧫ 掌握程度：前端日志收集 + 可视化标注回流训练  
 ⧫ 落地场景：外卖 App 语音点餐“我要一份中辣的黄焖鸡”→ 意图=下单，槽位=菜品+辣度  

9  情感识别与情感管理（Emotion Recognition & Management）  
 ⧫ 掌握程度：监听用户输入情绪，动态切换 UI 色调与回复策略  
 ⧫ 落地场景：在线教育辅导机器人检测到挫败情绪，推送鼓励语  

10 对话级知识推理（Knowledge Reasoning over Turns）  
 ⧫ 掌握程度：前端缓存知识图谱片段，减少重复请求  
 ⧫ 落地场景：医疗问诊根据多轮症状推理可能疾病并提示  

11 多语言指代规则适配（Multilingual Coreference）  
 ⧫ 掌握程度：根据 i18n locale 切换规则包  
 ⧫ 落地场景：跨境电商平台中/英/西语客服  

12 语义相似度实时计算（Online Semantic Similarity）  
 ⧫ 掌握程度：浏览器端使用 transformers.js + WebGPU 计算 embedding  
 ⧫ 落地场景：FAQ 自动推荐，用户输入即出现相似问题答案  

13 用户输入纠错（Auto-Correction & Spell-Check）  
 ⧫ 掌握程度：集成 hunspell.js，支持自定义词典  
 ⧫ 落地场景：搜索框即时提示“拼写错误，是否要找 ‘JavaScript’”  

14 语音转文本（ASR）与文本转语音（TTS）浏览器 API  
 ⧫ 掌握程度：熟练使用 Web Speech API + 自定义唤醒词  
 ⧫ 落地场景：驾驶模式语音助手  

15 语音活动检测（VAD）与断句策略  
 ⧫ 掌握程度：调整 VAD 阈值，避免截断  
 ⧫ 落地场景：会议实时字幕  

16 多模态输入融合（语音+文本+图像）  
 ⧫ 掌握程度：用 FileReader + Canvas 提取图像特征  
 ⧫ 落地场景：用户说“这件衣服多少钱”+上传照片→商品识别  

17 敏感信息实时过滤（PII Masking）  
 ⧫ 掌握程度：前端正则+后端双重校验  
 ⧫ 落地场景：银行客服自动脱敏身份证号  

18 输入速率限制与防刷（Rate-Limit & Anti-Spam）  
 ⧫ 掌握程度：前端滑动窗口计数 + 图形验证码  
 ⧫ 落地场景：免费试用 API 防刷  

19 客户端提示词缓存（Prompt Cache via LocalStorage/IndexedDB）  
 ⧫ 掌握程度：LRU 策略 + 版本号校验  
 ⧫ 落地场景：离线模式仍可复用提示模板  

20 输入令牌（Token）实时计数与计费提示  
 ⧫ 掌握程度：集成 tiktoken-wasm，即时显示费用  
 ⧫ 落地场景：按量付费的 AI 写作工具  

————————————————————————————————  
Ⅱ. 对话式 UI 组件（用户界面）  
21 Chat 对话容器（Chat Container）  
22 TypingBubble 打字机气泡  
23 Think 思考中组件（CoT 可视化）  
24 Typing 输入中状态指示  
25 MessageStatus 消息发送状态（已发/已读/失败/重试）  
26 虚拟滚动（Virtual Scrolling）  
27 消息分组与折叠（Message Grouping & Collapse）  
28 富文本/Markdown 安全渲染（XSS-Free Markdown）  
29 代码高亮与行号（Syntax Highlight with line-numbers）  
30 可交互代码沙箱（Runnable Code Snippet via Sandpack）  
31 引用消息回复（Reply with Quote）  
32 消息转发/分享（Forward/Share）  
33 消息删除/撤回（Delete/Recall）  
34 消息编辑（Edit）  
35 拖拽上传文件（Drag-and-Drop Upload）  
36 粘贴图片自动 OCR 上传  
37 深色/浅色主题一键切换（CSS Variables + prefers-color-scheme）  
38 无障碍支持（WCAG 2.1 AA；ARIA live region）  
39 国际化（i18n）与 RTL 布局镜像  
40 组件级懒加载（Code-Splitting by Route/Module）  
41 主题在线可视化编辑器（Theme Editor）  
42 聊天记录导出（HTML/PDF/Markdown）  
43 聊天历史搜索（Full-Text Search with IndexedDB）  
44 表情/贴纸面板（Sticker Picker with Lazy Load）  
45 快捷指令面板（Slash Commands Palette）  
46 语音波形动画（Audio Waveform Visualization）  
47 实时音视频通话浮窗（Picture-in-Picture）  
48 AI 卡片模板（Adaptive Card / Form Card）  
49 悬浮球入口（Chat Widget / PWA Bubble）  

掌握程度：能独立封装为 npm 包；提供 Storybook 示例；支持 SSR/SSG。  
落地场景：企业级客服 SaaS、AI Copilot 插件、嵌入式聊天 SDK。

————————————————————————————————  
Ⅲ. 流式解析层（网络与数据）  
50 Server-Sent Events（SSE）  
51 WebSocket 全双工流  
52 Fetch ReadableStream（HTTP/2 Streaming）  
53 NDJSON 解析（Newline-Delimited JSON）  
54 LangGraph 流式处理机制（Graph-Based Streaming）  
55 智能流式混合模式（Adaptive Streaming Mode）  
56 流式版本控制（State Snapshot Versioning）  
57 边缘计算集成（Edge WASM Stream Worker）  
58 流式机器学习（Online partial_fit）  
59 Token 级节流（Token-Level Throttling via RAF）  
60 反压控制（Back-Pressure with Flow Control Frame）  
61 断线重连 & 指数退避（Exponential Backoff Reconnect）  
62 心跳保活（WebSocket Ping/Pong；SSE Retry Header）  
63 AbortController 取消请求（Ongoing Request Abort）  
64 竞态处理（Race-Condition Guarding）  
65 流式错误边界（Streaming Error Boundary UI）  
66 网络降级（Long-Polling Fallback）  
67 客户端缓存策略（SWR / React-Query Streaming）  
68 数据压缩（Per-Message Deflate / Brotli Stream）  
69 二进制流传输（ArrayBuffer / Protobuf over WebSocket）  
70 客户端水印（Watermark-Based Sync Point）  
71 端到端加密流（E2EE Streaming via WebCrypto）  
72 日志回放（rrweb Record & Replay for Debugging）  
73 性能监控（Web-Vitals + Custom Streaming Metrics）  
74 流式 SSR（Server-Side Rendering with Suspense）  
75 流式导入（Dynamic Import + HTTP Streaming）  
76 流式 Web Worker（Comlink + ReadableStream）  
77 多路复用（Multiplexing Streams over Single Connection）  

掌握程度：可手写解析器；熟悉 HTTP/2、WebSocket RFC；能在 DevTools 网络面板定位流式帧。  
落地场景：ChatGPT 类实时回答、直播弹幕、股票行情推送。

————————————————————————————————  
Ⅳ. LLM 推理与网关  
78 大模型推理服务（vLLM、TensorRT-LLM、TGI）  
 ⧫ 掌握程度：会用 Docker 起容器，前端通过 OpenAPI 调通  
 ⧫ 落地场景：私有化部署客服模型  

79 LLM 网关 / 聚合 API（APIPark、One-API）  
 ⧫ 掌握程度：配置路由、限流、熔断；前端统一 baseURL  
 ⧫ 落地场景：多模型（GPT-4o、Claude-3.5）按优先级调度  

80 提示词模板管理（PromptHub、PromptLayer、Helicone）  
 ⧫ 掌握程度：前端通过 REST/SDK 读取版本化模板  
 ⧫ 落地场景：运营可随时改提示词而无需发版  

81 提示词 A/B 测试与灰度发布平台  
 ⧫ 掌握程度：前端埋点 + 后端分流；能看实时转化率  
 ⧫ 落地场景：不同提示词对电商导购 GMV 的影响  

82 模型评估 & 回归测试（LLMEval、TruLens）  
 ⧫ 掌握程度：能跑内置指标，前端展示评分雷达图  
 ⧫ 落地场景：上线前自动回归 1000 条测试问句  

83 上下文长度压缩（RAG + 摘要、LongLoRA）  
 ⧫ 掌握程度：前端提示“已启用长文压缩，可能影响精度”  
 ⧫ 落地场景：50 页 PDF 问答  

————————————————————————————————  
Ⅴ. Agent 与工具链  
84 Function Call / Tool Use 协议（OpenAI、MCP）  
 ⧫ 掌握程度：前端声明 JSON Schema，动态生成表单  
 ⧫ 落地场景：天气 Agent 调用 get_weather(city)  

85 多 Agent 协作协议（A2A、AutoGen GroupChat）  
 ⧫ 掌握程度：前端可视化编排节点连线  
 ⧫ 落地场景：招聘流程：HR Agent → 面试官 Agent → 评价 Agent  

86 前端 Agent SDK（@cloudbase/aiagent-framework、AG-UI Client）  
 ⧫ 掌握程度：会初始化、注册工具、监听生命周期事件  
 ⧫ 落地场景：小程序内嵌智能助手  

87 低代码可视化 Agent 构建器（拖拽式节点 + 代码沙箱）  
 ⧫ 掌握程度：扩展自定义节点组件  
 ⧫ 落地场景：运营无代码创建“优惠券发放机器人”  

88 端侧推理（WebGPU + TensorFlow.js、ORT-web）  
 ⧫ 掌握程度：能跑 7B 量化模型，FPS>15  
 ⧫ 落地场景：离线语音助手  

89 模型版本热更新（Model Diff + IndexedDB 缓存）  
 ⧫ 掌握程度：实现 diff 下载、校验哈希  
 ⧫ 落地场景：浏览器自动升级本地轻量模型  

90 联邦学习 / 联邦推理（WebRTC 数据通道 + Secure Aggregation）  
 ⧫ 掌握程度：理解加密聚合流程，前端只传梯度  
 ⧫ 落地场景：跨医院医疗模型协作  

————————————————————————————————  
Ⅵ. 业务与数据治理  
91 业务意图 Schema（名称、描述、示例三元组）  
 ⧫ 掌握程度：维护 YAML/JSON Schema，前端自动生成表单验证  
 ⧫ 落地场景：银行转账意图校验  

92 业务领域 RAG（向量检索 + 混合检索 + 精排）  
 ⧫ 掌握程度：前端调 search API，展示置信度条  
 ⧫ 落地场景：企业知识库问答  

93 人机协同 (Human-in-the-Loop) 决策 UI  
 ⧫ 掌握程度：一键“转人工”+ 会话上下文透传  
 ⧫ 落地场景：复杂售后工单  

94 敏感内容过滤 (Guardrails、Rebuff) 前端接入  
 ⧫ 掌握程度：实时标红 + 阻止发送  
 ⧫ 落地场景：儿童教育 App  

95 成本监控 & Token 计费仪表盘（Grafana 插件）  
 ⧫ 掌握程度：用 WebSocket 推送实时费用到前端  
 ⧫ 落地场景：SaaS 租户看板  

96 三方业务容器（小程序 / 微前端 / 插件化）  
 ⧫ 掌握程度：qiankun/Module Federation 接入对话组件  
 ⧫ 落地场景：钉钉小程序 AI 助理  

97 动态化脚本下发（JS-Runtime + 安全沙箱）  
 ⧫ 掌握程度：使用 QuickJS-WASM 运行受信脚本  
 ⧫ 落地场景：节假活动临时变更对话逻辑  

98 日志回放（rrweb Record & Replay for Debugging）  
 ⧫ 掌握程度：一键分享会话回放链接  
 ⧫ 落地场景：复现用户报错  

—— 完 ——