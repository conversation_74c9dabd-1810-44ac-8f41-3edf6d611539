# JavaScript基础面试题

## 数据类型与变量

1. JS的基本数据类型
2. ES6新增了什么数据类型，ES2020新增的数据类型
3. let和const
4. let和var
5. js代码输出题（考察var let const）var未定义报什么错，let未定义报什么错，为什么错误不一样
6. symbol概念及用途

## 循环与迭代

7. for in 和 for of 的区别
8. 实现一个可迭代类

## 对象与函数

9. 如何创建一个对象
10. Js创建对象的方式
11. New关键字的原理，具体做了什么操作
12. 如何定义a，可满足a=1&a=2&a=3条件成立，可以使用proxy实现吗

## 闭包与作用域

13. 闭包特性，一定是函数套函数吗，除了可访问外层变量还有什么特性，项目中有哪些地方用到了闭包
14. 讲讲闭包，什么情况下会使用闭包？
15. 手写闭包
16. 闭包会造成什么问题？内存溢出会造成什么问题？（追问）

## 异步编程

17. 事件循环
18. JS的事件循环，如果当前同时存在宏任务和微任务是先清空微任务队列还是怎么样
19. 实现一个promise
20. 事件循环输出题

## 数组操作

21. 会修改原数组和不会修改原数组的方法
22. unshift 作用
23. 数组扁平
24. 对象数组去重

## 字符串操作

25. 字符串指定位置插入字符
26. substr()的调用输出

## 事件处理

27. JS的事件处理机制
28. 事件冒泡、事件捕获、事件委托
29. evt.target和evt.currentTarget的区别
30. 冒泡机制和捕获机制谁先谁后
31. 如何阻止事件冒泡
32. 如何阻止默认事件

## 性能与优化

33. 防抖节流
34. 手写防抖
35. JS的垃圾回收机制
36. 讲讲深拷贝和浅拷贝

## ES6+特性

37. 项目中有用到哪些es6特性
38. 函数传参是深拷贝还是浅拷贝

## 其他

39. set和map
