# 前端面试题整理

本文件夹包含了从多个面试经历中整理出来的前端面试题目，按技术领域进行分类。

## 文件结构

- **01-Vue框架相关.md** - Vue.js相关的面试题
- **02-JavaScript基础.md** - JavaScript基础知识面试题  
- **03-CSS样式相关.md** - CSS样式和布局相关面试题
- **04-浏览器与性能.md** - 浏览器机制、性能优化相关面试题
- **05-网络与安全.md** - 网络协议、安全防护相关面试题
- **06-算法编程题.md** - 算法和编程实现题
- **07-工程化工具.md** - 构建工具、工程化相关面试题
- **08-综合面试经验.md** - 综合能力、项目经验、个人发展相关面试题

## 使用说明

每个文件都只包含题目列表，方便快速浏览和复习。可以根据自己的薄弱环节重点准备相应的技术领域。

## 面试准备建议

1. **技术基础** - 重点掌握JavaScript、Vue、CSS等核心技术
2. **项目经验** - 准备好项目中的技术难点和解决方案
3. **算法能力** - 练习常见的算法和数据结构题目
4. **综合素质** - 准备好个人发展、团队协作等软技能问题

祝面试顺利！🚀
